import pty from "node-pty";

const proxy = "http://127.0.0.1:7897";
const initialPrompt = "请优化 utils 文件夹中的所有函数为 async/await 风格，并检查是否可以合并重复逻辑。";

const env = {
  ...process.env,
  http_proxy: proxy,
  https_proxy: proxy,
};

const ptyProcess = pty.spawn("claude", ["agent"], {
  name: "xterm-color",
  cols: 100,
  rows: 30,
  cwd: process.cwd(),
  env,
});

let ready = false;
let idleTimer = null;
const idleTimeoutMs = 2000; // 连续 2 秒无输出

function sendPrompt(prompt) {
  // 清空当前输入行（Ctrl+U）
  ptyProcess.write("\u0015");

  // 输入文本
  ptyProcess.write(prompt);

  // 模拟延迟后按下回车（更稳妥）
  setTimeout(() => {
    ptyProcess.write("\r");
  }, 300);
}

function resetIdleTimer() {
  if (idleTimer) clearTimeout(idleTimer);
  idleTimer = setTimeout(() => {
    if (!ready) {
      ready = true;
      console.log("\n✅ 检测到 2s 无输出，Claude Agent 可能已 ready，发送任务...\n");
      sendPrompt(initialPrompt);
    }
  }, idleTimeoutMs);
}

// 启动时就开启 idle 计时
resetIdleTimer();

// 输出监听
ptyProcess.onData((data) => {
  process.stdout.write(data);
  resetIdleTimer();
});
